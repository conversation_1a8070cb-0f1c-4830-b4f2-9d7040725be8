<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.laien.web.biz.proj.oog101.mapper.ProjSevenmWorkoutGenerateMapper">

    <sql id="queryTemplate">
        FROM proj_sevenm_workout_generate wg
        <if test="pageReq.videoIdList != null and pageReq.videoIdList.size() > 0">
            JOIN proj_sevenm_workout_generate_exercise_video v ON v.proj_sevenm_workout_generate_id = wg.id
        </if>
        WHERE
        wg.del_flag = 0
        <if test="pageReq.videoIdList != null and pageReq.videoIdList.size() > 0">
            AND v.del_flag = 0
            AND v.proj_sevenm_exercise_video_id IN
            <foreach item="item" collection="pageReq.videoIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReq.projSevenmTemplateId != null">
            AND wg.proj_sevenm_template_id = #{pageReq.projSevenmTemplateId}
        </if>
        <if test="pageReq.projSevenmTemplateTaskId != null">
            AND wg.proj_sevenm_template_task_id = #{pageReq.projSevenmTemplateTaskId}
        </if>
        <if test="pageReq.id != null">
            AND wg.id = #{pageReq.id}
        </if>
        <if test="pageReq.ids != null and pageReq.ids.size() > 0">
            AND wg.id IN
            <foreach item="item" collection="pageReq.ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReq.status != null">
            AND wg.status = #{pageReq.status}
        </if>
        <if test="pageReq.fileStatus != null">
            AND wg.file_status = #{pageReq.fileStatus}
        </if>
        <if test="templateIdSet != null and templateIdSet.size() > 0">
            AND wg.proj_sevenm_template_id IN
            <foreach item="item" collection="templateIdSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pageReq.difficulty != null">
            AND wg.difficulty = #{pageReq.difficulty}
        </if>
        <if test="pageReq.equipment != null">
            AND wg.equipment = #{pageReq.equipment}
        </if>
        <if test="pageReq.projId!= null">
            AND wg.proj_id = #{pageReq.projId}
        </if>
        <if test="pageReq.gender != null">
            AND wg.gender = #{pageReq.gender}
        </if>
        <if test="specialLimitSum != null and specialLimitSum != 0">
            AND wg.special_limit &amp; #{specialLimitSum} = #{specialLimitSum}
        </if>
        <if test="targetSum != null and targetSum != 0">
            AND wg.target &amp; #{targetSum} = #{targetSum}
        </if>
        GROUP BY wg.id
        ORDER BY wg.id DESC
    </sql>

    <select id="page" resultType="java.lang.Integer">
        SELECT
            wg.id
        <include refid="queryTemplate" />

    </select>
    <select id="list" resultType="java.lang.Integer">
        SELECT
            wg.id
        <include refid="queryTemplate" />
    </select>

</mapper>
